#!/bin/bash

# Setup Custom Domain for ADK Analyst on Google Cloud Platform
# Usage: ./gcp-deployment/setup-custom-domain.sh [domain]

set -e

# Configuration
PROJECT_ID="truxtsaas"
REGION="us-central1"
SERVICE_NAME="adk-analyst-jenkins-agent-production"
DOMAIN=${1:-"agents.trucks.ai"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

show_usage() {
    echo "Usage: $0 [domain]"
    echo ""
    echo "This script sets up a custom domain for the ADK Analyst Cloud Run service."
    echo ""
    echo "Parameters:"
    echo "  domain    Custom domain to map (default: agents.trucks.ai)"
    echo ""
    echo "Examples:"
    echo "  $0 agents.trucks.ai"
    echo "  $0 adk-analyst.trucks.ai"
    echo ""
    echo "Prerequisites:"
    echo "  - Domain ownership verification in Google Cloud Console"
    echo "  - Cloud Run service deployed"
    echo "  - Appropriate IAM permissions"
}

check_prerequisites() {
    log_step "Checking prerequisites..."
    
    # Check gcloud CLI
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed"
        exit 1
    fi
    
    # Check authentication
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "Not authenticated with gcloud"
        echo "Run: gcloud auth login"
        exit 1
    fi
    
    # Check project
    current_project=$(gcloud config get-value project 2>/dev/null || echo "")
    if [[ "$current_project" != "$PROJECT_ID" ]]; then
        log_warning "Current project: $current_project, expected: $PROJECT_ID"
        log_info "Setting project to $PROJECT_ID"
        gcloud config set project "$PROJECT_ID"
    fi
    
    # Check if Cloud Run service exists
    if ! gcloud run services describe "$SERVICE_NAME" --region="$REGION" &>/dev/null; then
        log_error "Cloud Run service '$SERVICE_NAME' not found in region '$REGION'"
        echo "Please deploy the service first using: ./gcp-deployment/deploy-to-gcp.sh cloud-run production"
        exit 1
    fi
    
    log_success "Prerequisites check completed"
}

verify_domain_ownership() {
    log_step "Verifying domain ownership..."
    
    log_info "Checking if domain '$DOMAIN' is verified in Google Cloud..."
    
    # Check if domain is already verified
    if gcloud domains list-user-verified --filter="domain:$DOMAIN" --format="value(domain)" | grep -q "$DOMAIN"; then
        log_success "Domain '$DOMAIN' is already verified"
        return 0
    fi
    
    log_warning "Domain '$DOMAIN' is not verified in Google Cloud"
    echo ""
    echo "To verify domain ownership:"
    echo "1. Go to: https://console.cloud.google.com/apis/credentials/domainverification"
    echo "2. Click 'Add Domain'"
    echo "3. Enter domain: $DOMAIN"
    echo "4. Follow verification instructions (DNS TXT record or HTML file)"
    echo ""
    echo "For trucks.ai domain, you may need to verify the parent domain first."
    echo ""
    
    read -p "Have you verified the domain ownership? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_error "Domain verification required before proceeding"
        exit 1
    fi
    
    log_success "Domain ownership confirmed"
}

create_domain_mapping() {
    log_step "Creating domain mapping..."
    
    log_info "Mapping domain '$DOMAIN' to Cloud Run service '$SERVICE_NAME'..."
    
    # Create domain mapping
    if gcloud run domain-mappings create \
        --service="$SERVICE_NAME" \
        --domain="$DOMAIN" \
        --region="$REGION" \
        --quiet; then
        log_success "Domain mapping created successfully"
    else
        log_error "Failed to create domain mapping"
        return 1
    fi
    
    # Wait a moment for the mapping to be processed
    sleep 5
    
    # Get the domain mapping details
    log_info "Getting domain mapping details..."
    gcloud run domain-mappings describe "$DOMAIN" --region="$REGION"
}

get_dns_records() {
    log_step "Getting DNS configuration..."
    
    log_info "Retrieving DNS records for domain mapping..."
    
    # Get the CNAME record details
    local cname_target=$(gcloud run domain-mappings describe "$DOMAIN" \
        --region="$REGION" \
        --format="value(status.resourceRecords[0].rrdata)" 2>/dev/null || echo "")
    
    if [[ -n "$cname_target" ]]; then
        log_success "DNS records retrieved"
        echo ""
        echo "📋 DNS Configuration Required:"
        echo "================================"
        echo ""
        echo "Add the following CNAME record to your DNS provider (Cloudflare):"
        echo ""
        echo "Type: CNAME"
        echo "Name: agents"
        echo "Target: $cname_target"
        echo "TTL: Auto (or 300 seconds)"
        echo ""
        echo "Full domain: $DOMAIN"
        echo "Points to: $cname_target"
        echo ""
    else
        log_warning "Could not retrieve CNAME target. Domain mapping may still be processing."
        echo ""
        echo "You can check the DNS records later with:"
        echo "gcloud run domain-mappings describe $DOMAIN --region=$REGION"
    fi
}

setup_ssl_certificate() {
    log_step "Setting up SSL certificate..."
    
    log_info "Google Cloud Run automatically provisions SSL certificates for custom domains"
    log_info "SSL certificate will be automatically created and managed"
    
    echo ""
    echo "📋 SSL Certificate Information:"
    echo "==============================="
    echo ""
    echo "✅ Automatic SSL: Google Cloud Run will automatically provision"
    echo "✅ Certificate Type: Google-managed SSL certificate"
    echo "✅ Renewal: Automatic renewal before expiration"
    echo "✅ Protocols: TLS 1.2 and TLS 1.3 supported"
    echo ""
    echo "The SSL certificate will be provisioned after DNS propagation (5-60 minutes)"
}

check_service_status() {
    log_step "Checking service status..."
    
    # Get current service URL
    local service_url=$(gcloud run services describe "$SERVICE_NAME" \
        --region="$REGION" \
        --format="value(status.url)")
    
    echo ""
    echo "📊 Service Status:"
    echo "=================="
    echo ""
    echo "Service Name: $SERVICE_NAME"
    echo "Region: $REGION"
    echo "Current URL: $service_url"
    echo "Custom Domain: $DOMAIN"
    echo "Status: $(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="value(status.conditions[0].status)")"
    echo ""
}

show_next_steps() {
    log_step "Next steps and verification..."
    
    echo ""
    echo "🎯 Next Steps:"
    echo "=============="
    echo ""
    echo "1. 📋 Add DNS Record in Cloudflare:"
    echo "   - Login to Cloudflare dashboard"
    echo "   - Go to trucks.ai domain DNS settings"
    echo "   - Add CNAME record: agents -> [CNAME target from above]"
    echo ""
    echo "2. ⏱️  Wait for DNS Propagation (5-60 minutes):"
    echo "   - DNS changes need time to propagate globally"
    echo "   - SSL certificate will be provisioned automatically"
    echo ""
    echo "3. 🧪 Test the Domain:"
    echo "   - curl -I https://$DOMAIN"
    echo "   - dig $DOMAIN"
    echo "   - nslookup $DOMAIN"
    echo ""
    echo "4. 🌐 Access Your Service:"
    echo "   - https://$DOMAIN"
    echo "   - The ADK Analyst web interface will be available"
    echo ""
    echo "📞 Verification Commands:"
    echo "========================"
    echo ""
    echo "# Check domain mapping status"
    echo "gcloud run domain-mappings describe $DOMAIN --region=$REGION"
    echo ""
    echo "# Check DNS resolution"
    echo "dig $DOMAIN"
    echo "nslookup $DOMAIN"
    echo ""
    echo "# Test HTTPS connectivity"
    echo "curl -I https://$DOMAIN"
    echo ""
    echo "🔍 Troubleshooting:"
    echo "==================="
    echo ""
    echo "If the domain doesn't work after DNS propagation:"
    echo "1. Verify DNS record is correct in Cloudflare"
    echo "2. Check domain mapping: gcloud run domain-mappings list --region=$REGION"
    echo "3. Verify domain ownership in Google Cloud Console"
    echo "4. Check service logs: gcloud run services logs read $SERVICE_NAME --region=$REGION"
}

main() {
    local domain=${1:-$DOMAIN}
    
    echo "🌐 ADK Analyst Custom Domain Setup"
    echo "=================================="
    echo ""
    
    # Show usage if help requested
    if [[ "$1" == "-h" || "$1" == "--help" ]]; then
        show_usage
        exit 0
    fi
    
    log_info "Setting up custom domain: $domain"
    log_info "Cloud Run service: $SERVICE_NAME"
    log_info "Region: $REGION"
    log_info "Project: $PROJECT_ID"
    echo ""
    
    # Confirm setup
    read -p "Proceed with domain setup for '$domain'? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Domain setup cancelled"
        exit 0
    fi
    
    # Run setup steps
    check_prerequisites
    verify_domain_ownership
    create_domain_mapping
    get_dns_records
    setup_ssl_certificate
    check_service_status
    show_next_steps
    
    echo ""
    log_success "🎉 Domain setup completed!"
    echo ""
    echo "Your ADK Analyst service will be available at: https://$domain"
    echo "After DNS propagation and SSL certificate provisioning."
}

# Run main function with all arguments
main "$@"
